import { convertFileToBase64 } from './images';

/**
 * Converts a base64 image string to ImageData for processing
 * @param base64String - Base64 encoded image string
 * @returns Promise<ImageData> - ImageData object containing pixel data
 */
const base64ToImageData = (base64String: string): Promise<ImageData> => {
  return new Promise((resolve, reject) => {
    // Create an image element
    const img = new Image();

    img.onload = () => {
      // Create a canvas to extract pixel data
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');

      if (!ctx) {
        reject(new Error('Failed to get canvas context'));
        return;
      }

      canvas.width = img.width;
      canvas.height = img.height;

      // Draw the image on canvas
      ctx.drawImage(img, 0, 0);

      // Get pixel data
      const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
      resolve(imageData);
    };

    img.onerror = () => {
      reject(new Error('Failed to load image'));
    };

    // Set the source to the base64 string
    img.src = `data:image/png;base64,${base64String}`;
  });
};

/**
 * Converts ImageData to grayscale
 * @param imageData - ImageData object containing RGBA pixel data
 * @returns Uint8Array - Grayscale pixel values
 */
const convertToGrayscale = (imageData: ImageData): Uint8Array => {
  const { data, width, height } = imageData;
  const grayscale = new Uint8Array(width * height);

  for (let i = 0; i < data.length; i += 4) {
    // Convert RGBA to grayscale using luminance formula
    const r = data[i];
    const g = data[i + 1];
    const b = data[i + 2];

    // Standard luminance formula
    const gray = Math.round(0.299 * r + 0.587 * g + 0.114 * b);
    grayscale[i / 4] = gray;
  }

  return grayscale;
};

/**
 * Applies Laplacian kernel to detect edges
 * @param grayscaleData - Grayscale pixel data
 * @param width - Image width
 * @param height - Image height
 * @returns number[] - Laplacian filtered values
 */
const applyLaplacianKernel = (
  grayscaleData: Uint8Array,
  width: number,
  height: number,
): number[] => {
  // Laplacian kernel (8-connected)
  const kernel = [
    [0, -1, 0],
    [-1, 4, -1],
    [0, -1, 0],
  ];

  const result: number[] = [];

  // Apply kernel to each pixel (excluding border pixels)
  for (let y = 1; y < height - 1; y++) {
    for (let x = 1; x < width - 1; x++) {
      let sum = 0;

      // Apply 3x3 kernel
      for (let ky = -1; ky <= 1; ky++) {
        for (let kx = -1; kx <= 1; kx++) {
          const pixelIndex = (y + ky) * width + (x + kx);
          const kernelValue = kernel[ky + 1][kx + 1];
          sum += grayscaleData[pixelIndex] * kernelValue;
        }
      }

      result.push(sum);
    }
  }

  return result;
};

/**
 * Calculates the variance of an array of numbers
 * @param values - Array of numerical values
 * @returns number - Variance of the values
 */
const calculateVariance = (values: number[]): number => {
  if (values.length === 0) return 0;

  // Calculate mean
  const mean = values.reduce((sum, val) => sum + val, 0) / values.length;

  // Calculate variance
  const variance = values.reduce((sum, val) => {
    const diff = val - mean;
    return sum + diff * diff;
  }, 0) / values.length;

  return variance;
};

/**
 * Calculates the Laplacian variance of an image from a file path
 * Higher values indicate sharper images, lower values indicate blurrier images
 * @param imagePath - Path to the image file
 * @returns Promise<number | null> - Laplacian variance value or null if error
 */
export const calculateLaplacianVarianceFromPath = async (
  imagePath: string,
): Promise<number | null> => {
  try {
    // Convert image file to base64
    const base64String = await convertFileToBase64(imagePath);
    if (!base64String) {
      console.error('Failed to convert image to base64');
      return null;
    }

    return await calculateLaplacianVarianceFromBase64(base64String);
  } catch (error) {
    console.error('Error calculating Laplacian variance from path:', error);
    return null;
  }
};

/**
 * Calculates the Laplacian variance of an image from a base64 string
 * Higher values indicate sharper images, lower values indicate blurrier images
 * @param base64String - Base64 encoded image string
 * @returns Promise<number | null> - Laplacian variance value or null if error
 */
export const calculateLaplacianVarianceFromBase64 = async (
  base64String: string,
): Promise<number | null> => {
  try {
    // Convert base64 to ImageData
    const imageData = await base64ToImageData(base64String);

    // Convert to grayscale
    const grayscaleData = convertToGrayscale(imageData);

    // Apply Laplacian kernel
    const laplacianValues = applyLaplacianKernel(
      grayscaleData,
      imageData.width,
      imageData.height,
    );

    // Calculate variance
    const variance = calculateVariance(laplacianValues);

    return variance;
  } catch (error) {
    console.error('Error calculating Laplacian variance from base64:', error);
    return null;
  }
};

/**
 * Determines if an image is blurry based on Laplacian variance
 * @param variance - Laplacian variance value
 * @param threshold - Threshold value (default: 100, adjust based on testing)
 * @returns boolean - True if image is considered blurry
 */
export const isImageBlurry = (variance: number, threshold: number = 100): boolean => {
  return variance < threshold;
};

/**
 * Analyzes image blur from file path and returns detailed results
 * @param imagePath - Path to the image file
 * @param threshold - Blur threshold (default: 100)
 * @returns Promise<BlurAnalysisResult | null> - Analysis results or null if error
 */
export interface BlurAnalysisResult {
  variance: number;
  isBlurry: boolean;
  threshold: number;
  sharpnessScore: 'very_sharp' | 'sharp' | 'moderate' | 'blurry' | 'very_blurry';
}

export const analyzeImageBlur = async (
  imagePath: string,
  threshold: number = 100,
): Promise<BlurAnalysisResult | null> => {
  try {
    const variance = await calculateLaplacianVarianceFromPath(imagePath);

    if (variance === null) {
      return null;
    }

    const isBlurry = isImageBlurry(variance, threshold);

    // Determine sharpness score based on variance ranges
    let sharpnessScore: BlurAnalysisResult['sharpnessScore'];
    if (variance > 500) {
      sharpnessScore = 'very_sharp';
    } else if (variance > 200) {
      sharpnessScore = 'sharp';
    } else if (variance > 100) {
      sharpnessScore = 'moderate';
    } else if (variance > 50) {
      sharpnessScore = 'blurry';
    } else {
      sharpnessScore = 'very_blurry';
    }

    return {
      variance,
      isBlurry,
      threshold,
      sharpnessScore,
    };
  } catch (error) {
    console.error('Error analyzing image blur:', error);
    return null;
  }
};