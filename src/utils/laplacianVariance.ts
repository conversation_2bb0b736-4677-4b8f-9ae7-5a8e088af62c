import { convertFileToBase64 } from './images';

/**
 * React Native compatible Laplacian variance implementation
 * This is a simplified approach that works with base64 image data
 * For full pixel-level analysis, consider using react-native-opencv
 */

/**
 * Calculates a blur metric from base64 image data using byte variance analysis
 * This approximates Laplacian variance by analyzing data patterns in the compressed image
 * @param base64String - Base64 encoded image string
 * @returns number - A blur metric (higher values = sharper, lower values = blurrier)
 */
const calculateBlurMetricFromBase64 = (base64String: string): number => {
  try {
    // Convert base64 to binary data
    const binaryString = atob(base64String);
    const bytes = new Uint8Array(binaryString.length);

    for (let i = 0; i < binaryString.length; i++) {
      bytes[i] = binaryString.charCodeAt(i);
    }

    // Analyze byte patterns - sharp images have more variation
    // We'll look at differences between adjacent bytes as a proxy for edge content
    let edgeSum = 0;
    let edgeCount = 0;
    const sampleStep = Math.max(1, Math.floor(bytes.length / 5000)); // Sample for performance

    for (let i = 0; i < bytes.length - 1; i += sampleStep) {
      const diff = Math.abs(bytes[i] - bytes[i + 1]);
      edgeSum += diff * diff; // Square the differences to emphasize larger changes
      edgeCount++;
    }

    // Calculate average squared difference (variance-like metric)
    const averageEdgeVariance = edgeCount > 0 ? edgeSum / edgeCount : 0;

    // Scale the result to make it more interpretable
    return averageEdgeVariance * 10;
  } catch (error) {
    console.error('Error calculating blur metric from base64:', error);
    return 0;
  }
};

/**
 * Calculates the variance of an array of numbers
 * @param values - Array of numerical values
 * @returns number - Variance of the values
 */
const calculateVariance = (values: number[]): number => {
  if (values.length === 0) return 0;

  // Calculate mean
  const mean = values.reduce((sum, val) => sum + val, 0) / values.length;

  // Calculate variance
  const variance = values.reduce((sum, val) => {
    const diff = val - mean;
    return sum + diff * diff;
  }, 0) / values.length;

  return variance;
};

/**
 * Calculates the Laplacian variance of an image from a file path
 * Higher values indicate sharper images, lower values indicate blurrier images
 * @param imagePath - Path to the image file
 * @returns Promise<number | null> - Laplacian variance value or null if error
 */
export const calculateLaplacianVarianceFromPath = async (
  imagePath: string,
): Promise<number | null> => {
  try {
    // Convert image file to base64
    const base64String = await convertFileToBase64(imagePath);
    if (!base64String) {
      console.error('Failed to convert image to base64');
      return null;
    }

    return calculateLaplacianVarianceFromBase64(base64String);
  } catch (error) {
    console.error('Error calculating Laplacian variance from path:', error);
    return null;
  }
};

/**
 * Calculates the Laplacian variance of an image from a base64 string
 * Higher values indicate sharper images, lower values indicate blurrier images
 * @param base64String - Base64 encoded image string
 * @returns number | null - Laplacian variance value or null if error
 */
export const calculateLaplacianVarianceFromBase64 = (
  base64String: string,
): number | null => {
  try {
    // Use the simplified blur metric calculation
    const blurMetric = calculateBlurMetricFromBase64(base64String);
    return blurMetric;
  } catch (error) {
    console.error('Error calculating Laplacian variance from base64:', error);
    return null;
  }
};

/**
 * Determines if an image is blurry based on Laplacian variance
 * @param variance - Laplacian variance value
 * @param threshold - Threshold value (default: 100, adjust based on testing)
 * @returns boolean - True if image is considered blurry
 */
export const isImageBlurry = (variance: number, threshold: number = 100): boolean => {
  return variance < threshold;
};

/**
 * Analyzes image blur from file path and returns detailed results
 * @param imagePath - Path to the image file
 * @param threshold - Blur threshold (default: 100)
 * @returns Promise<BlurAnalysisResult | null> - Analysis results or null if error
 */
export interface BlurAnalysisResult {
  variance: number;
  isBlurry: boolean;
  threshold: number;
  sharpnessScore: 'very_sharp' | 'sharp' | 'moderate' | 'blurry' | 'very_blurry';
}

export const analyzeImageBlur = async (
  imagePath: string,
  threshold: number = 100,
): Promise<BlurAnalysisResult | null> => {
  try {
    const variance = await calculateLaplacianVarianceFromPath(imagePath);

    if (variance === null) {
      return null;
    }

    const isBlurry = isImageBlurry(variance, threshold);

    // Determine sharpness score based on variance ranges
    let sharpnessScore: BlurAnalysisResult['sharpnessScore'];
    if (variance > 500) {
      sharpnessScore = 'very_sharp';
    } else if (variance > 200) {
      sharpnessScore = 'sharp';
    } else if (variance > 100) {
      sharpnessScore = 'moderate';
    } else if (variance > 50) {
      sharpnessScore = 'blurry';
    } else {
      sharpnessScore = 'very_blurry';
    }

    return {
      variance,
      isBlurry,
      threshold,
      sharpnessScore,
    };
  } catch (error) {
    console.error('Error analyzing image blur:', error);
    return null;
  }
};