import ImageResizer, {
  ResizeFormat,
} from '@bam.tech/react-native-image-resizer';
import { readAsync } from '@lodev09/react-native-exify';
import { Platform } from 'react-native';
import RNFS from 'react-native-fs';

const IMAGE_EXTENSIONS = ['jpg', 'jpeg', 'png', 'avif', 'webp'];

const isImageType = (fileName: string) => {
  return IMAGE_EXTENSIONS.some(type =>
    fileName.toLowerCase().endsWith(`.${type}`),
  );
};

const compressImageFromUri = async (
  uri: string,
  format: ResizeFormat = 'JPEG',
  targetWidth: number = 800,
  targetHeight: number = 800,
  compressionPercentage: number = 40,
): Promise<string | null> => {
  try {
    if (!uri) {
      console.error(
        'imageUtils.ts: compressImageFromUri(): URI is undefined, or empty',
      );
      return null;
    }

    const orientation = await getExifOrientation(uri);
    const rotation =
      Platform.OS === 'ios'
        ? 0
        : mapExifOrientationToRotation(orientation ?? 1);

    const result = await ImageResizer.createResizedImage(
      uri,
      targetWidth,
      targetHeight,
      format,
      compressionPercentage,
      rotation,
      undefined,
      false,
      {
        mode: 'cover',
        onlyScaleDown: true,
      },
    );
    deleteTempFile(uri);

    return result?.uri;
  } catch (error) {
    console.error(
      'imageUtils.ts: compressImageFromUri(): Failed to compress image',
      error,
    );
    return null;
  }
};

const getExifOrientation = async (
  imagePath: string,
): Promise<number | undefined> => {
  try {
    const tags = await readAsync(`file://${imagePath}`);
    return tags?.Orientation;
  } catch (error) {
    console.error(
      'imageUtils.ts: getExifOrientation(): Failed to read EXIF data',
      error,
    );
    return undefined;
  }
};

const mapExifOrientationToRotation = (orientation: number): number => {
  switch (orientation) {
    case 3:
      return 180;
    case 6:
      return 90;
    case 8:
      return 270;
    default:
      return 0;
  }
};

const removeFilesInBatch = async (
  filePaths: string[],
  batchSize: number = 10,
) => {
  for (let i = 0; i < filePaths.length; i += batchSize) {
    const batch = filePaths.slice(i, i + batchSize);
    const promises = batch.map(filePath => deleteTempFile(filePath));

    try {
      await Promise.allSettled(promises);
    } catch (error) {
      console.error(
        'imageUtils.ts: removeFilesInBatch(): Failed to delete files in batch',
        error,
      );
      throw error;
    }
  }
};

export const getRandomImgFile = async () => {
  const imageArray = [
    // Red 1x1 PNG
    'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8z8BQDwAEhQGAhKmMIQAAAABJRU5ErkJggg==',
    // Blue 1x1 PNG
    'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg==',
    // Green 1x1 PNG
    'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
    // Yellow 1x1 PNG
    'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg==',
    // Purple 1x1 PNG
    'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg==',
    // Orange 1x1 PNG
    'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg==',
    // White 1x1 PNG
    'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg==',
    // Black 1x1 PNG
    'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg==',
  ];
  const randomIndex = Math.floor(Math.random() * imageArray.length);

  const randomImg = imageArray[randomIndex];

  return randomImg;
};

export const convertFileToBase64 = async (
  filePath: string,
): Promise<string | null> => {
  try {
    return await RNFS.readFile(filePath, 'base64');
  } catch (error) {
    console.error(
      error,
      'imageUtils.ts: convertFileToBase64(): Failed to read file as base64 string',
    );
    return null;
  }
};

export const deleteTempFile = async (filePath: string) => {
  try {
    const path = filePath.includes('file://')
      ? filePath.replace('file://', '')
      : filePath;

    if (path?.length) {
      const exists = await RNFS.exists(path);
      if (exists) {
        await RNFS.unlink(path);
      }
    }
  } catch (error) {
    console.error(
      'imageUtils.ts: deleteTempFile(): Failed to delete temp file',
      error,
    );
    throw error;
  }
};

const compressImageAndConvertToBase64 = async (imagePath: string) => {
  try {
    const compressedImagePath = await compressImageFromUri(imagePath);
    if (!compressedImagePath) {
      console.error(
        'imageUtils.ts: compressImageAndConvertToBase64(): Failed to compress image',
      );
      return null;
    }
    const base64 = await convertFileToBase64(compressedImagePath);
    if (!base64) {
      console.error(
        'imageUtils.ts: compressImageAndConvertToBase64(): Failed to convert image to base64',
      );
      return null;
    }
    return base64;
  } catch (error) {
    console.error(
      'imageUtils.ts: compressImageAndConvertToBase64(): Failed to compress image and convert to base64',
      error,
    );
    return null;
  }
};

async function deleteCachedImages() {
  try {
    const exists = await RNFS.exists(RNFS.CachesDirectoryPath);
    if (!exists) {
      console.info(
        'imageUtils.ts: deleteCachedImages(): CachesDirectoryPath does not exist',
      );
      return;
    }
    const files = await RNFS.readDir(RNFS.CachesDirectoryPath);
    for (const file of files) {
      if (file.isFile() && isImageType(file.name)) {
        const filePath = [RNFS.CachesDirectoryPath, file.name].join('/');
        try {
          await RNFS.unlink(filePath);
        } catch (error) {
          console.error(
            `imageUtils.ts: deleteCachedImages(): Failed to delete file ${filePath}:`,
            error,
          );
        }
      }
    }
  } catch (error) {
    console.error(
      'imageUtils.ts: deleteCachedImages(): Failed to delete cached images',
      error,
    );
  }
}

export const ImageTitle = {
  proofOfService: 'proofOfService',
  photosTaken: 'photosTaken',
  parcel: 'parcel',
  photoEmptyLockbox: 'photoEmptyLockbox',
  photoSNOTag: 'photoSNOTag',
  photoLockboxArea: 'photoLockboxArea',
  photoPerimeter: 'photoPerimeter',
  emptyCoolerInVehicleStepExamplePhotos:
    'emptyCoolerInVehicleStepExamplePhotos',
  areaWhereCoolerIsStoredPhotos: 'areaWhereCoolerIsStoredPhotos',
  areaWhereSamplesAreHandledPhotos: 'areaWhereSamplesAreHandledPhotos',
  dropOffSignature: 'dropOffSignature',
  thermometerPhoto: 'thermometerPhoto',
  lockboxPhoto: 'lockboxPhoto',
  parcelBarcodeBypassPhoto: 'parcelBarcodeBypassPhoto',
} as const;

export const ImageTitleInfo: Record<
  keyof typeof ImageTitle,
  { title: string; multiple: boolean }
> = {
  [ImageTitle.proofOfService]: {
    title: 'proof_of_service',
    multiple: false,
  },
  [ImageTitle.photosTaken]: {
    title: 'photos_taken',
    multiple: true,
  },
  [ImageTitle.parcel]: {
    title: 'parcel',
    multiple: true,
  },
  [ImageTitle.photoEmptyLockbox]: {
    title: 'sno_empty_lockbox',
    multiple: false,
  },
  [ImageTitle.photoSNOTag]: {
    title: 'sno_tag_left',
    multiple: false,
  },
  [ImageTitle.photoLockboxArea]: {
    title: 'sno_area_around_lockbox',
    multiple: false,
  },
  [ImageTitle.photoPerimeter]: {
    title: 'perimeter_check',
    multiple: true,
  },
  [ImageTitle.emptyCoolerInVehicleStepExamplePhotos]: {
    title: 'survey_empty_cooler',
    multiple: false,
  },
  [ImageTitle.areaWhereCoolerIsStoredPhotos]: {
    title: 'survey_cooler_stored',
    multiple: false,
  },
  [ImageTitle.areaWhereSamplesAreHandledPhotos]: {
    title: 'survey_samples_handled',
    multiple: false,
  },
  [ImageTitle.dropOffSignature]: {
    title: 'dropoff_signature',
    multiple: false,
  },
  [ImageTitle.thermometerPhoto]: {
    title: 'lockbox_thermometer_photo',
    multiple: false,
  },
  [ImageTitle.lockboxPhoto]: {
    title: 'lockbox_photo',
    multiple: false,
  },
  [ImageTitle.parcelBarcodeBypassPhoto]: {
    title: 'parcel_barcode_bypass_photo',
    multiple: true,
  },
} as const;

// Interface for image data structure compatible with React Native
interface ImagePixelData {
  data: Uint8Array | number[];
  width: number;
  height: number;
}

/**
 * Calculates the Laplacian variance of an image to measure blur/sharpness
 * Higher values indicate sharper images, lower values indicate more blur
 * @param imageData - Image pixel data with RGBA values
 * @returns Promise<number> - The Laplacian variance value
 */
export const calculateLaplacianVariance = async (
  imageData: ImagePixelData,
): Promise<number> => {
  const { data, width, height } = imageData;

  // Convert to grayscale first
  const grayscale = new Array(width * height);
  for (let i = 0; i < data.length; i += 4) {
    const pixelIndex = i / 4;
    const r = data[i];
    const g = data[i + 1];
    const b = data[i + 2];
    // Standard grayscale conversion formula
    grayscale[pixelIndex] = 0.299 * r + 0.587 * g + 0.114 * b;
  }

  // Apply Laplacian kernel
  // Laplacian kernel: [0, -1, 0; -1, 4, -1; 0, -1, 0]
  const laplacian = new Array(width * height).fill(0);

  for (let y = 1; y < height - 1; y++) {
    for (let x = 1; x < width - 1; x++) {
      const index = y * width + x;

      // Apply Laplacian kernel
      const value =
        4 * grayscale[index] -
        grayscale[index - 1] -           // left
        grayscale[index + 1] -           // right
        grayscale[index - width] -       // top
        grayscale[index + width];        // bottom

      laplacian[index] = value;
    }
  }

  // Calculate variance of Laplacian values
  const mean = laplacian.reduce((sum, val) => sum + val, 0) / laplacian.length;
  const variance = laplacian.reduce((sum, val) => {
    const diff = val - mean;
    return sum + diff * diff;
  }, 0) / laplacian.length;

  return variance;
};

/**
 * Creates a simple test image data for testing the Laplacian variance algorithm
 * This generates synthetic image data to validate the blur detection logic
 * @param width - Image width
 * @param height - Image height
 * @param pattern - Pattern type: 'sharp' for high contrast, 'blur' for low contrast
 * @returns ImagePixelData - Synthetic image data for testing
 */
export const createTestImageData = (
  width: number = 100,
  height: number = 100,
  pattern: 'sharp' | 'blur' | 'gradient' = 'sharp',
): ImagePixelData => {
  const data = new Array(width * height * 4); // RGBA

  for (let y = 0; y < height; y++) {
    for (let x = 0; x < width; x++) {
      const index = (y * width + x) * 4;

      let value: number;

      switch (pattern) {
        case 'sharp':
          // Create a checkerboard pattern (high contrast = sharp)
          value = ((x + y) % 20 < 10) ? 255 : 0;
          break;
        case 'blur':
          // Create a smooth gradient (low contrast = blurry)
          value = Math.sin(x * 0.1) * Math.sin(y * 0.1) * 127 + 127;
          break;
        case 'gradient':
          // Create a linear gradient
          value = (x / width) * 255;
          break;
        default:
          value = 128;
      }

      // Set RGBA values
      data[index] = value;     // R
      data[index + 1] = value; // G
      data[index + 2] = value; // B
      data[index + 3] = 255;   // A (fully opaque)
    }
  }

  return { data, width, height };
};

/**
 * Converts base64 image data to pixel data for processing
 * This is a placeholder that demonstrates the expected interface
 * In a production app, you would integrate with react-native-image-manipulator or similar
 * @param base64Data - Base64 encoded image data
 * @returns Promise<ImagePixelData | null> - Pixel data or null if failed
 */
export const decodeBase64ToPixelData = async (
  _base64Data: string,
): Promise<ImagePixelData | null> => {
  try {
    // For now, return null to indicate this needs proper implementation
    // In a real implementation, you would:
    // 1. Decode the base64 to binary data
    // 2. Use a library like react-native-image-manipulator to get pixel data
    // 3. Convert to the ImagePixelData format

    console.warn(
      'decodeBase64ToPixelData needs implementation with image processing library. ' +
      'Use createTestImageData() for testing the algorithm.'
    );

    return null;
  } catch (error) {
    console.error('Error in decodeBase64ToPixelData:', error);
    return null;
  }
};

/**
 * Test function to demonstrate and validate the Laplacian variance calculation
 * This can be used to verify the algorithm works correctly
 * @returns Promise<void>
 */
export const testLaplacianVariance = async (): Promise<void> => {
  console.log('Testing Laplacian Variance Algorithm...');

  try {
    // Test with sharp image (should have high variance)
    const sharpImage = createTestImageData(50, 50, 'sharp');
    const sharpVariance = await calculateLaplacianVariance(sharpImage);
    console.log(`Sharp image variance: ${sharpVariance.toFixed(2)}`);

    // Test with blurry image (should have low variance)
    const blurImage = createTestImageData(50, 50, 'blur');
    const blurVariance = await calculateLaplacianVariance(blurImage);
    console.log(`Blur image variance: ${blurVariance.toFixed(2)}`);

    // Test with gradient image (should have medium variance)
    const gradientImage = createTestImageData(50, 50, 'gradient');
    const gradientVariance = await calculateLaplacianVariance(gradientImage);
    console.log(`Gradient image variance: ${gradientVariance.toFixed(2)}`);

    // Verify that sharp images have higher variance than blurry ones
    if (sharpVariance > blurVariance) {
      console.log('✅ Algorithm working correctly: Sharp > Blur variance');
    } else {
      console.log('❌ Algorithm issue: Sharp variance should be higher than blur');
    }

    console.log('Laplacian Variance test completed');
  } catch (error) {
    console.error('Error testing Laplacian variance:', error);
  }
};

/**
 * Convenience function to calculate Laplacian variance directly from base64 image
 * @param base64Data - Base64 encoded image data
 * @returns Promise<number | null> - Laplacian variance or null if failed
 */
export const calculateBlurFromBase64 = async (
  base64Data: string,
): Promise<number | null> => {
  try {
    const pixelData = await decodeBase64ToPixelData(base64Data);
    if (!pixelData) {
      return null;
    }

    const variance = await calculateLaplacianVariance(pixelData);
    return variance;
  } catch (error) {
    console.error('Failed to calculate blur from base64:', error);
    return null;
  }
};

/**
 * Convenience function to calculate Laplacian variance from image file URI
 * @param uri - Image file URI
 * @returns Promise<number | null> - Laplacian variance or null if failed
 */
export const calculateBlurFromUri = async (
  uri: string,
): Promise<number | null> => {
  try {
    // First convert the image to base64
    const base64Data = await convertFileToBase64(uri);
    if (!base64Data) {
      console.error('Failed to convert image to base64');
      return null;
    }

    // Then calculate blur from base64
    return await calculateBlurFromBase64(base64Data);
  } catch (error) {
    console.error('Failed to calculate blur from URI:', error);
    return null;
  }
};

export const filterValidImageUris = (images: any[]): string[] =>
  images
    .filter(img => img?.uri && typeof img.uri === 'string')
    .map(img => img.uri);

export {
  compressImageFromUri,
  removeFilesInBatch,
  compressImageAndConvertToBase64,
  deleteCachedImages,
};
