import React, { useState, useEffect } from 'react';
import { ScrollView, StyleSheet, View, Text, Alert } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useBottomTabBarHeight } from '@react-navigation/bottom-tabs';
import Slider from '~/components/inputs/Slider';
import StopTimer from '~/components/stoptimer/StopTimer';
import colors from '~/styles/colors';
import CheckboxGroup from '~/components/checkbox/CheckboxGroup';
import FilledButton from '~/components/buttons/FilledButton';
import { primarySolidButton } from '~/styles/buttons';
import ScreenWrapper from '~/screens/ScreenWrapper';
import { Divider } from '@rneui/base';
import { verticalSpace16 } from '~/styles/spacing';
import SearchableDropdownSelect from '~/components/select/dropdown/SearchableDropdownSelect';
import TextInput from '~/components/inputs/TextInput';
import { ArrowUpDown, Box, Email, EyeClosed } from '~/components/icons';
import Title from '~/components/text/Title';
import en from '~/localization/en';
import ToastMessage from '~/components/toast/ToastMessage';
import { CheckInCard } from '~/components/cards/CheckInCard';
import InfoItem from '~/components/info/InfoItem';
import LocationPinOutlined from '~/components/icons/LocationPinOutlined';
import Notes from '~/components/icons/Notes';
import MapView from '~/components/maps/MapView';
import ProofOfService from '~/components/ProofOfService';

import {
  BlurAnalysisResult
} from '~/utils/laplacianVariance';

const TestScreen = () => {
  const { bottom } = useSafeAreaInsets();
  const bottomTabBarHeight = useBottomTabBarHeight();

  const [allChecked, setAllChecked] = useState(false);
  const [selectedValue, setSelectedValue] = useState<string | null>(null);
  const [defaultInput, setDefaultInput] = useState('');
  const [secureInput, setSecureInput] = useState('');
  const [showBanner, setShowBanner] = useState(true);

  const [isTimerActive, setIsTimerActive] = useState(false);
  const [isButtonEnabled, setIsButtonEnabled] = useState(false);

  // Laplacian variance testing state
  const [testImagePath, setTestImagePath] = useState<string>('');
  const [blurAnalysis, setBlurAnalysis] = useState<BlurAnalysisResult | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);

  useEffect(() => {
    setIsTimerActive(true);
  }, []);

  const handleComplete = () => {
    setIsButtonEnabled(true);
  };

  // Laplacian variance testing handlers
  const loadSampleImage = (imagePath: string) => {
    setTestImagePath(imagePath);
    setBlurAnalysis(null);
    Alert.alert('Sample Image Loaded', `Image path set to: ${imagePath}`);
  };

  const analyzeCurrentImage = async () => {
    if (!testImagePath.trim()) {
      Alert.alert('No Image Path', 'Please enter an image path first');
      return;
    }

    setIsAnalyzing(true);
    try {
      // Import the utility functions
      const { calculateLaplacianVarianceFromBase64 } = require('~/utils/laplacianVariance');

      // For bundled assets, we need to use a different approach
      // Let's try to create a base64 string from the bundled asset
      let base64String = '';
      let analysisResult: BlurAnalysisResult | null = null;

      // Check if this is a bundled asset path
      if (testImagePath.includes('src/assets/images/')) {
        try {
          // For demonstration, let's create some sample base64 data
          // In a real app, you'd need to use react-native-fs or similar to read bundled assets
          const sampleBase64 = createSampleImageData(testImagePath);
          const variance = calculateLaplacianVarianceFromBase64(sampleBase64);

          if (variance !== null) {
            analysisResult = {
              variance: variance,
              isBlurry: variance < 100,
              threshold: 100,
              sharpnessScore: getSharpnessScore(variance)
            };
          }
        } catch (assetError) {
          console.log('Could not process as bundled asset:', assetError);
        }
      }

      if (analysisResult) {
        setBlurAnalysis(analysisResult);

        Alert.alert(
          'Blur Analysis Results',
          `Path: ${testImagePath}\nVariance: ${analysisResult.variance.toFixed(2)}\nSharpness: ${analysisResult.sharpnessScore}\nIs Blurry: ${analysisResult.isBlurry ? 'Yes' : 'No'}`,
          [{ text: 'OK' }]
        );
      } else {
        // If analysis fails, show a mock result for demonstration
        const mockAnalysis: BlurAnalysisResult = {
          variance: Math.random() * 1000,
          isBlurry: Math.random() > 0.5,
          threshold: 100,
          sharpnessScore: ['very_sharp', 'sharp', 'moderate', 'blurry', 'very_blurry'][
            Math.floor(Math.random() * 5)
          ] as BlurAnalysisResult['sharpnessScore']
        };

        setBlurAnalysis(mockAnalysis);

        Alert.alert(
          'Mock Analysis Results',
          `Could not analyze actual file, showing mock data.\nPath: ${testImagePath}\nVariance: ${mockAnalysis.variance.toFixed(2)}\nSharpness: ${mockAnalysis.sharpnessScore}`,
          [{ text: 'OK' }]
        );
      }

    } catch (error) {
      console.error('Error analyzing image:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      Alert.alert('Error', `Failed to analyze image: ${errorMessage}`);
    } finally {
      setIsAnalyzing(false);
    }
  };

  // Helper function to create sample image data for testing
  const createSampleImageData = (imagePath: string): string => {
    // Create different patterns based on the image name to simulate different blur levels
    const fileName = imagePath.split('/').pop() || imagePath;
    let pattern = '';

    if (fileName === 'test_sharp_image') {
      // Simulate a very sharp image with high variance - lots of edge content
      pattern = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()_+-=[]{}|;:,.<>?';
    } else if (fileName === 'test_blurry_image') {
      // Simulate a very blurry image with low variance - minimal variation
      pattern = 'aaaaaabbbbbbccccccddddddeeeeeeffffffgggggghhhhhhiiiiii';
    } else if (fileName.includes('empty')) {
      // Simulate a sharp image with high variance
      pattern = 'sharp_pattern_with_high_edge_content_and_lots_of_variation_between_adjacent_pixels_ABCD1234';
    } else if (fileName.includes('cooler_area')) {
      // Simulate a moderately sharp image
      pattern = 'moderate_pattern_with_some_edge_content_and_medium_variation_abcd5678';
    } else {
      // Default: simulate a blurry image with low variance
      pattern = 'blurry_pattern_with_low_edge_content_and_minimal_variation_aaaa1111';
    }

    // Convert to base64 for testing - repeat pattern to create more data
    return btoa(pattern.repeat(200));
  };

  const getSharpnessScore = (variance: number): BlurAnalysisResult['sharpnessScore'] => {
    if (variance > 500) return 'very_sharp';
    if (variance > 200) return 'sharp';
    if (variance > 100) return 'moderate';
    if (variance > 50) return 'blurry';
    return 'very_blurry';
  };

  const scrollContainerStyle = {
    ...styles.scrollContainer,
    paddingBottom:
      bottomTabBarHeight +
      (bottom || styles.scrollContainer.padding) +
      styles.scrollContainer.padding +
      32,
  };

  return (
    <ScreenWrapper isKeyboardSensitive>
      <ScreenWrapper.Body>
        <ScrollView
          contentContainerStyle={scrollContainerStyle}
          showsVerticalScrollIndicator={false}>
          <View style={styles.div}>
            <Title title={'Lockbox temperature'} icon={null} />
            <Title
              title={en.exchange}
              icon={<ArrowUpDown color={colors.darkBlue600} />}
            />
            <Title
              title={'Samples not out'}
              subtitle="Confirm you have knocked and share who you spoke to at the clinic"
              icon={null}
              showProgressText={true}
              progressText={'1/4'}
            />
            <Title
              title={'Parcel details'}
              icon={<Box color={colors.darkBlue600} />}
              textButtonVisible={true}
              textButtonTitle={'Add parcels'}
              onTextButtonPress={() => {}}
            />

            <MapView coordinate={[-112.0016533, 33.6401096]} />

            <Divider style={verticalSpace16} />

            <InfoItem
              title={'Getting there'}
              infoText={'4280 W Maple Ave, Seattle, WA 98101'}
              icon={<LocationPinOutlined />}
              enableCopy={true}
            />
            <InfoItem
              title={'Things to know'}
              infoText={
                'Lockbox is in the alley. Open until 7pm if no answer knock on door.'
              }
              icon={<Notes />}
            />

            <Divider style={verticalSpace16} />
          </View>
          {showBanner && (
            <ToastMessage
              variant="banner"
              type="warning"
              message="Current stops need to be finished before this one."
              linkText="Go to current stop"
              onPress={() => setShowBanner(false)}
              testID="ToastMessage.Banner"
            />
          )}

          <Divider style={verticalSpace16} />

          <FilledButton
            title="Show Banner"
            testID="TestScreen.Button.ShowBanner"
            onClick={() => setShowBanner(true)}
            id="TestScreen.Button.ShowBanner"
            style={primarySolidButton}
            color="primary"
          />

          <Divider style={verticalSpace16} />

          <CheckInCard
            title="Survey - Check In"
            description="Complete before timer ends"
            durationInMinutes={1}
            isTimerActive={isTimerActive}
            startTimestamp={Date.now()}
            isButtonEnabled={isButtonEnabled}
            onPress={() => console.log('Check-In Completed')}
            onComplete={handleComplete}
          />

          <Divider style={verticalSpace16} />

          <Slider
            id={'Slider'}
            min={50}
            max={104}
            step={6}
            defaultValue={77}
            title={'Lockbox temperature'}
            suffix={'°F'}
          />

          <Divider style={verticalSpace16} />

          <StopTimer
            durationInMinutes={1}
            strokeColor={colors.darkBlue600}
            id={'StopTimer'}
          />

          <Divider style={verticalSpace16} />

          <CheckboxGroup
            options={[
              {
                label: 'At least 10 frozen ice packs',
                value: 'CheckboxGroup.Checkbox.IcePacks',
              },
              {
                label: 'Disposable insulated pads',
                value: 'CheckboxGroup.Checkbox.InsulatedPads',
              },
              {
                label: 'Insulated cooler',
                value: 'CheckboxGroup.Checkbox.Cooler',
              },
            ]}
            onAllChecked={setAllChecked}
          />

          <FilledButton
            isDisabled={!allChecked}
            id={'TestScreen.Button.Continue'}
            title={'Continue'}
            onClick={() => console.log('Next Step')}
            style={primarySolidButton}
            color={'primary'}
          />

          <Divider style={verticalSpace16} />

          <View>
            <SearchableDropdownSelect
              id={'SearchableDropdownSelect'}
              label={'Searchable Dropdown Select'}
              placeholderText={'Select an option'}
              items={[
                'Option 1',
                'Option 2',
                'Option 3',
                'Option 4',
                'Option 5',
                'Option 6',
                'Option 7',
              ]}
              onValueSelection={setSelectedValue}
              selectedValue={selectedValue}
              searchPlaceHolderText={''}
            />
          </View>

          <Divider style={verticalSpace16} />

          <TextInput
            id={'TestScreen.InputField.email'}
            label="Email"
            placeholder="Email address"
            value={defaultInput}
            onChangeText={setDefaultInput}
            errorMessage="Email cannot be empty"
            icon={<Email />}
          />
          <TextInput
            id={'TestScreen.InputField.password'}
            label="Password"
            placeholder="Password"
            value={secureInput}
            onChangeText={setSecureInput}
            secureTextEntry={true}
            icon={<EyeClosed />}
            hint="Minimum 8 characters"
          />

          <Divider style={verticalSpace16} />

          {/* Laplacian Variance Testing Section */}
          <Title title="Laplacian Variance Testing" icon={null} />

          <TextInput
            id="TestScreen.InputField.imagePath"
            label="Image Path"
            placeholder="Enter image path (e.g., src/assets/images/survey_empty_cooler.webp)"
            value={testImagePath}
            onChangeText={setTestImagePath}
            hint="Enter the path to an image file in the repository"
          />

          <View style={styles.buttonRow}>
            <FilledButton
              title="Sharp Test"
              testID="TestScreen.Button.SharpTest"
              onClick={() => loadSampleImage('test_sharp_image')}
              id="TestScreen.Button.SharpTest"
              style={[primarySolidButton, styles.halfButton]}
              color="secondary"
            />

            <FilledButton
              title="Blurry Test"
              testID="TestScreen.Button.BlurryTest"
              onClick={() => loadSampleImage('test_blurry_image')}
              id="TestScreen.Button.BlurryTest"
              style={[primarySolidButton, styles.halfButton]}
              color="secondary"
            />
          </View>

          <View style={styles.buttonRow}>
            <FilledButton
              title="Asset Image 1"
              testID="TestScreen.Button.Asset1"
              onClick={() => loadSampleImage('src/assets/images/survey_empty_cooler.webp')}
              id="TestScreen.Button.Asset1"
              style={[primarySolidButton, styles.halfButton]}
              color="secondary"
            />

            <FilledButton
              title="Asset Image 2"
              testID="TestScreen.Button.Asset2"
              onClick={() => loadSampleImage('src/assets/images/survey_cooler_area.webp')}
              id="TestScreen.Button.Asset2"
              style={[primarySolidButton, styles.halfButton]}
              color="secondary"
            />
          </View>

          <FilledButton
            title={isAnalyzing ? "Analyzing..." : "Analyze Blur"}
            testID="TestScreen.Button.AnalyzeBlur"
            onClick={analyzeCurrentImage}
            id="TestScreen.Button.AnalyzeBlur"
            style={primarySolidButton}
            color="primary"
            isDisabled={isAnalyzing || !testImagePath.trim()}
          />

          {blurAnalysis && (
            <View style={styles.analysisResults}>
              <Text style={styles.analysisTitle}>Blur Analysis Results:</Text>
              <Text style={styles.analysisText}>Variance: {blurAnalysis.variance.toFixed(2)}</Text>
              <Text style={styles.analysisText}>Sharpness Score: {blurAnalysis.sharpnessScore}</Text>
              <Text style={styles.analysisText}>Is Blurry: {blurAnalysis.isBlurry ? 'Yes' : 'No'}</Text>
              <Text style={styles.analysisText}>Threshold: {blurAnalysis.threshold}</Text>
            </View>
          )}

          <Divider style={verticalSpace16} />

          <ProofOfService
            onImgCapture={() => {}}
            onImgSelect={() => {}}
            onCommentChange={() => {}}
            commentValue={''}
            onProofOfServiceChange={() => {}}
            proofOfServiceValue={''}
          />
        </ScrollView>
      </ScreenWrapper.Body>
    </ScreenWrapper>
  );
};

const styles = StyleSheet.create({
  scrollContainer: {
    marginTop: 32,
    padding: 16,
    width: '100%',
  },
  div: {
    paddingTop: 16,
    gap: 16,
  },
  analysisResults: {
    backgroundColor: colors.backgroundLight,
    padding: 16,
    borderRadius: 8,
    marginTop: 8,
  },
  analysisTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.darkBlue600,
    marginBottom: 8,
  },
  analysisText: {
    fontSize: 14,
    color: colors.darkBlue600,
    marginBottom: 4,
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 8,
  },
  halfButton: {
    flex: 1,
  },
});

export default TestScreen;
